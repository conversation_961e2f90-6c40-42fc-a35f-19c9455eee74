# 时间轴缩放和滚动功能

## 新增功能

### 🔍 时间轴缩放功能
- **Alt + 鼠标滚轮**：缩放时间轴
  - 向上滚动：放大时间轴（显示更多细节）
  - 向下滚动：缩小时间轴（显示更大范围）
  - 缩放时会保持鼠标位置对应的时间点不变

### 📏 精确到帧的缩放
- 最大缩放级别可以精确到每一帧（假设30fps）
- 在高缩放级别下：
  - 时间刻度显示帧数（格式：MM:SS:FF）
  - 网格线间隔调整为每秒一条，同时显示帧级别网格线
  - **帧时长指示条**：每个帧刻度右侧显示淡黄色渐变条，表示该帧的时长
  - 指示条宽度随缩放级别动态调整，放得越大条越长
  - 可以进行逐帧编辑

### ↔️ 水平滚动功能
- **Shift + 鼠标滚轮**：水平滚动时间轴
  - 向上滚动：向左滚动
  - 向下滚动：向右滚动
  - 在放大状态下可以查看时间轴的不同部分

## 技术实现

### 缩放级别管理
- 最小缩放：0.1x（显示更大时间范围）
- 最大缩放：动态计算，确保每帧至少占用1像素
- 缩放时自动调整滚动偏移量以保持视图连续性

### 智能刻度显示
根据缩放级别自动调整时间刻度的精度：
- **高缩放（≥300px/秒）**：显示帧级别刻度（MM:SS:FF）+ 帧时长指示条
- **中等缩放（≥100px/秒）**：显示毫秒级别刻度（MM:SS.MS）
- **正常缩放**：显示秒级别刻度（MM:SS）

### 帧时长指示条
- **外观**：淡黄色渐变条，从刻度线右侧延伸
- **宽度**：占帧宽度的80%，随缩放级别动态调整
- **位置**：在时间刻度和时间轴网格中都会显示
- **交互**：鼠标悬停时颜色加深，提供更好的视觉反馈

### 网格线自适应
- 高缩放：每秒一条主网格线 + 帧级别网格线（带帧指示条）
- 中等缩放：每2秒一条网格线
- 正常缩放：每5秒一条网格线
- 低缩放：每10秒一条网格线

## 使用场景

### 精确编辑
1. 使用 Alt + 滚轮放大到最大级别
2. 可以看到每一帧的精确位置和时长指示条
3. 帧指示条帮助精确定位帧边界
4. 进行逐帧的精确剪切和调整

### 总览编辑
1. 使用 Alt + 滚轮缩小查看整体布局
2. 快速调整视频片段的大致位置
3. 查看整个项目的时间分布

### 导航浏览
1. 放大后使用 Shift + 滚轮水平滚动
2. 快速浏览时间轴的不同部分
3. 在保持精确度的同时查看不同时间段

## 注意事项

- 缩放和滚动功能在时间轴和时间刻度组件上都可用
- 视频片段的位置和大小会根据缩放级别自动调整
- 播放头位置始终准确显示当前播放时间
- 所有交互操作（拖拽、调整大小等）都考虑了缩放和滚动偏移
