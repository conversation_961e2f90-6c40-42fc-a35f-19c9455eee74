# 调试指南 - 帧指示器功能

## 🔧 调试步骤

### 1. 打开应用程序
访问：http://localhost:5173/

### 2. 上传视频文件
- 拖拽任意视频文件到时间轴区域
- 确保有视频片段在时间轴上

### 3. 打开浏览器开发者工具
- 按 F12 或右键 -> 检查
- 切换到 Console 标签页

### 4. 测试最大缩放
- 点击工具栏中的 **"最大缩放"** 按钮
- 观察控制台输出的调试信息

### 5. 手动缩放测试
- 按住 `Alt` 键 + 鼠标滚轮向上滚动
- 观察控制台的调试输出

## 📊 预期的调试输出

### 最大缩放级别计算
```
🎯 最大缩放级别计算:
  - 时间轴宽度: 800
  - 目标帧宽度 (1/20横幅): 40
  - 帧时长: 0.033333333333333333
  - 需要的像素/秒: 1200
  - 计算的最大缩放: 90
```

### 缩放设置
```
🔧 设置缩放级别:
  - 请求缩放: 90
  - 最大缩放: 90
  - 最小缩放: 0.1
  - 实际缩放: 90
  - 时间轴宽度: 800
```

### 帧指示器状态
```
🔍 TimeScale 帧指示器调试:
  - 容器宽度: 800
  - 缩放级别: 90
  - 总时长: 60
  - 像素/秒: 1200
  - 帧时长: 0.033333333333333333
  - 帧宽度: 40
  - 应该显示指示器: true

📏 帧指示条宽度: 32
```

## 🎯 检查要点

### 1. 缩放级别是否足够高
- 一帧应该占到容器宽度的 1/20 (5%)
- 在 800px 宽的容器中，一帧应该约为 40px

### 2. 帧指示器是否显示
- `应该显示指示器` 应该为 `true`
- `帧指示条宽度` 应该大于 0

### 3. 视觉检查
- 在时间刻度上应该能看到淡黄色的指示条
- 在时间轴网格中也应该有对应的指示条

## 🐛 常见问题排查

### 问题1：看不到黄色指示条
**可能原因：**
- 缩放级别不够高
- CSS 样式问题
- 帧宽度计算错误

**排查步骤：**
1. 检查控制台输出的 `帧宽度` 值
2. 确认 `应该显示指示器` 为 `true`
3. 检查浏览器开发者工具中的元素样式

### 问题2：缩放级别不够高
**可能原因：**
- 最大缩放级别计算错误
- 时间轴宽度获取错误

**排查步骤：**
1. 检查 `目标帧宽度` 是否为容器宽度的 1/20
2. 确认 `时间轴宽度` 值正确
3. 验证 `计算的最大缩放` 值

### 问题3：调试信息不显示
**可能原因：**
- 控制台被清空
- 组件未正确加载

**排查步骤：**
1. 刷新页面重新测试
2. 确认视频文件已上传
3. 检查是否有 JavaScript 错误

## 🔍 手动检查 CSS

如果指示条仍然不可见，可以在开发者工具中手动检查：

1. 在 Elements 标签页中搜索 `frame-duration-bar`
2. 检查元素是否存在且有正确的样式
3. 验证 `width` 属性是否大于 0
4. 检查 `background` 样式是否正确应用

## 📝 报告问题

如果问题仍然存在，请提供以下信息：
1. 控制台的完整调试输出
2. 浏览器类型和版本
3. 上传的视频文件信息
4. 开发者工具中的元素检查截图
